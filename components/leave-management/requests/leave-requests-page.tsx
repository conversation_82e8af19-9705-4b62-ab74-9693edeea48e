"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>ir<PERSON>, Filter, AlertCircle } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LeaveRequestsList } from "@/components/leave-management/leave-requests-list"
import { LeaveRequestDetails } from "@/components/leave-management/leave-request-details"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { LeaveRequest, ILeaveRequestDB } from "@/types/leave-request"
import { LeaveRequestFormOverlay } from "@/components/forms/overlays/leave-request-form-overlay"
import { useLeaveRequests } from "@/hooks/use-leave-requests"
import { format } from "date-fns"

interface LeaveRequestsPageProps {
  userId: string
}

export function LeaveRequestsPage({ userId }: LeaveRequestsPageProps) {
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null)
  const [filterStatus, setFilterStatus] = useState<string[]>(["all"])

  const {
    requests,
    loading,
    error,
    pagination,
    fetchRequests,
    refreshRequests,
    approveRequest,
    rejectRequest,
    rollbackRequest
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 50 },
    autoFetch: true
  });

  const handleRequestSelect = (request: LeaveRequest) => {
    setSelectedRequest(request)
  }

  const handleApproveRequest = async (id: string) => {
    const result = await approveRequest(id)
    if (result) {
      // Update the selected request if it's the one that was approved
      if (selectedRequest && selectedRequest.id === id) {
        setSelectedRequest({
          ...selectedRequest,
          status: 'approved'
        })
      }
      // Refresh the list to get updated data
      await refreshRequests()
    }
  }

  const handleRejectRequest = async (id: string, reason: string) => {
    const result = await rejectRequest(id, reason)
    if (result) {
      // Update the selected request if it's the one that was rejected
      if (selectedRequest && selectedRequest.id === id) {
        setSelectedRequest({
          ...selectedRequest,
          status: 'rejected'
        })
      }
      // Refresh the list to get updated data
      await refreshRequests()
    }
  }

  const handleRollbackRequest = async (id: string, reason: string) => {
    const result = await rollbackRequest(id, reason)
    if (result) {
      // Update the selected request if it's the one that was rolled back
      if (selectedRequest && selectedRequest.id === id) {
        setSelectedRequest({
          ...selectedRequest,
          status: 'pending'
        })
      }
      // Refresh the list to get updated data
      await refreshRequests()
    }
  }

  // Transform database requests to frontend format for compatibility
  const transformedRequests: LeaveRequest[] = useMemo(() => {
    return requests.map((request: ILeaveRequestDB) => ({
      id: request._id.toString(),
      employee: {
        id: (request.employeeId as any)?._id?.toString() || request.employeeId.toString(),
        name: `${(request.employeeId as any)?.firstName || ''} ${(request.employeeId as any)?.lastName || ''}`.trim() || 'Unknown Employee',
        position: (request.employeeId as any)?.position || 'Unknown Position',
        avatar: (request.employeeId as any)?.avatar || '/placeholder.svg',
      },
      type: (request.leaveTypeId as any)?.name || 'Unknown Type',
      startDate: format(new Date(request.startDate), 'yyyy-MM-dd'),
      endDate: format(new Date(request.endDate), 'yyyy-MM-dd'),
      days: request.duration,
      reason: request.reason,
      status: request.status as "pending" | "approved" | "rejected",
      requestDate: format(new Date(request.createdAt), 'yyyy-MM-dd'),
      reviewDate: request.approvalDate ? format(new Date(request.approvalDate), 'yyyy-MM-dd') : undefined,
      reviewedBy: request.approvedBy ? {
        id: (request.approvedBy as any)?._id?.toString() || request.approvedBy.toString(),
        name: `${(request.approvedBy as any)?.firstName || ''} ${(request.approvedBy as any)?.lastName || ''}`.trim() || 'Unknown Reviewer',
        avatar: (request.approvedBy as any)?.avatar || '/placeholder.svg',
      } : undefined,
      comments: request.rejectionReason,
    }))
  }, [requests]);

  // Filter requests based on status
  const filteredRequests = useMemo(() => {
    if (filterStatus.includes("all")) {
      return transformedRequests;
    }
    return transformedRequests.filter((request) => filterStatus.includes(request.status));
  }, [transformedRequests, filterStatus]);

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("all")}
                onCheckedChange={() => setFilterStatus(["all"])}
              >
                All Requests
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("pending")}
                onCheckedChange={() => {
                  if (filterStatus.includes("pending")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "pending"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "pending"])
                  }
                }}
              >
                Pending
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("approved")}
                onCheckedChange={() => {
                  if (filterStatus.includes("approved")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "approved"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "approved"])
                  }
                }}
              >
                Approved
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterStatus.includes("rejected")}
                onCheckedChange={() => {
                  if (filterStatus.includes("rejected")) {
                    setFilterStatus(filterStatus.filter((s) => s !== "rejected"))
                  } else {
                    setFilterStatus([...filterStatus.filter((s) => s !== "all"), "rejected"])
                  }
                }}
              >
                Rejected
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <LeaveRequestFormOverlay
          mode="create"
          trigger={
            <Button size="sm" className="h-8 gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>Request Leave</span>
            </Button>
          }
          onSuccess={refreshRequests}
        />
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="lg:col-span-1">
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
            <TabsContent value="pending" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests.filter((req) => req.status === "pending")}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
            <TabsContent value="approved" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests.filter((req) => req.status === "approved")}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
            <TabsContent value="all" className="mt-4">
              {loading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                  ))}
                </div>
              ) : (
                <LeaveRequestsList
                  requests={transformedRequests}
                  onRequestSelect={handleRequestSelect}
                  selectedRequestId={selectedRequest?.id}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
        <div className="lg:col-span-2">
          {selectedRequest ? (
            <LeaveRequestDetails
              request={selectedRequest}
              onApprove={handleApproveRequest}
              onReject={handleRejectRequest}
              onRollback={handleRollbackRequest}
              isLoading={loading}
            />
          ) : (
            <div className="flex h-[400px] items-center justify-center rounded-lg border border-dashed">
              <div className="flex flex-col items-center text-center">
                <h3 className="text-lg font-medium">No request selected</h3>
                <p className="text-sm text-muted-foreground">Select a request from the list to view details</p>
                <LeaveRequestFormOverlay
                  mode="create"
                  trigger={
                    <Button variant="outline" className="mt-4">
                      Create a new request
                    </Button>
                  }
                  onSuccess={refreshRequests}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
