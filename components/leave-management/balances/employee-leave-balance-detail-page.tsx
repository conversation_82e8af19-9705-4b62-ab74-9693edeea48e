"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  User, 
  Building, 
  Mail, 
  Phone,
  AlertCircle,
  PlusCircle,
  Download,
  History,
  TrendingUp,
  TrendingDown
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface EmployeeLeaveBalanceDetailPageProps {
  employeeId: string
  currentUserId: string
}

interface EmployeeDetails {
  _id: string
  firstName: string
  lastName: string
  employeeId: string
  email: string
  phone?: string
  department: string
  position: string
  hireDate: string
  employmentStatus: string
}

interface LeaveBalanceDetail {
  _id: string
  leaveTypeId: {
    _id: string
    name: string
    code: string
    color: string
    isPaid: boolean
    defaultDaysPerYear: number
  }
  totalDays: number
  usedDays: number
  pendingDays: number
  remainingDays: number
  carryOverDays: number
  year: number
  notes?: string
  createdAt: string
  updatedAt: string
}

interface LeaveRequest {
  _id: string
  leaveId: string
  startDate: string
  endDate: string
  duration: number
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  reason?: string
  leaveTypeId: {
    name: string
    color: string
  }
  approvedBy?: {
    firstName: string
    lastName: string
  }
  approvalDate?: string
  rejectionReason?: string
}

export function EmployeeLeaveBalanceDetailPage({ 
  employeeId, 
  currentUserId 
}: EmployeeLeaveBalanceDetailPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  
  const [employee, setEmployee] = useState<EmployeeDetails | null>(null)
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalanceDetail[]>([])
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [year, setYear] = useState<string>(new Date().getFullYear().toString())
  const [activeTab, setActiveTab] = useState("balances")

  // Generate year options (current year and 2 years back)
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 3 }, (_, i) => currentYear - i)

  useEffect(() => {
    fetchEmployeeData()
  }, [employeeId, year])

  const fetchEmployeeData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch employee details and leave balances
      const [employeeResponse, balancesResponse, requestsResponse] = await Promise.all([
        fetch(`/api/hr/employees/${employeeId}`),
        fetch(`/api/leave/balances?employeeId=${employeeId}&year=${year}`),
        fetch(`/api/hr/leave?employeeId=${employeeId}&year=${year}&limit=50`)
      ])

      if (!employeeResponse.ok) {
        throw new Error('Failed to fetch employee details')
      }

      if (!balancesResponse.ok) {
        throw new Error('Failed to fetch leave balances')
      }

      const employeeData = await employeeResponse.json()
      const balancesData = await balancesResponse.json()
      const requestsData = requestsResponse.ok ? await requestsResponse.json() : { docs: [] }

      setEmployee(employeeData) // HR employee endpoint returns employee directly
      setLeaveBalances(balancesData.data || [])
      setLeaveRequests(requestsData.docs || requestsData.data || [])

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching employee data'
      setError(errorMessage)
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleYearChange = (newYear: string) => {
    setYear(newYear)
  }

  const handleBack = () => {
    router.push('/dashboard/leave/balances')
  }

  const calculateTotals = () => {
    return leaveBalances.reduce(
      (totals, balance) => ({
        totalDays: totals.totalDays + balance.totalDays,
        usedDays: totals.usedDays + balance.usedDays,
        pendingDays: totals.pendingDays + balance.pendingDays,
        remainingDays: totals.remainingDays + balance.remainingDays,
        carryOverDays: totals.carryOverDays + balance.carryOverDays
      }),
      { totalDays: 0, usedDays: 0, pendingDays: 0, remainingDays: 0, carryOverDays: 0 }
    )
  }

  const totals = calculateTotals()
  const utilizationPercentage = totals.totalDays > 0 ? Math.round((totals.usedDays / totals.totalDays) * 100) : 0

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !employee) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleBack} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Employee not found'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleBack} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {employee.firstName} {employee.lastName}
            </h1>
            <p className="text-muted-foreground">
              {employee.employeeId} • {employee.department} • {employee.position}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select value={year} onValueChange={handleYearChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              {yearOptions.map(yearOption => (
                <SelectItem key={yearOption} value={yearOption.toString()}>
                  {yearOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" className="gap-1">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button size="sm" className="gap-1">
            <PlusCircle className="h-4 w-4" />
            Adjust Balance
          </Button>
        </div>
      </div>

      {/* Employee Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Employee Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">Email</div>
                <div className="font-medium">{employee.email}</div>
              </div>
            </div>
            {employee.phone && (
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Phone</div>
                  <div className="font-medium">{employee.phone}</div>
                </div>
              </div>
            )}
            <div className="flex items-center gap-3">
              <Building className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">Department</div>
                <div className="font-medium">{employee.department}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">Hire Date</div>
                <div className="font-medium">
                  {new Date(employee.hireDate).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Days</p>
                <p className="text-2xl font-bold">{totals.totalDays}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Used Days</p>
                <p className="text-2xl font-bold text-orange-600">{totals.usedDays}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Remaining</p>
                <p className="text-2xl font-bold text-green-600">{totals.remainingDays}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-amber-600">{totals.pendingDays}</p>
              </div>
              <Clock className="h-8 w-8 text-amber-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Utilization</p>
                <p className="text-2xl font-bold text-purple-600">{utilizationPercentage}%</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <Progress value={utilizationPercentage} className="w-8 h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 md:w-[600px]">
          <TabsTrigger value="balances">Leave Balances</TabsTrigger>
          <TabsTrigger value="requests">Leave Requests</TabsTrigger>
          <TabsTrigger value="history">Balance History</TabsTrigger>
        </TabsList>

        {/* Leave Balances Tab */}
        <TabsContent value="balances" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Leave Type Balances for {year}</CardTitle>
              <CardDescription>
                Detailed breakdown of leave balances by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              {leaveBalances.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No leave balances found for {year}. Contact HR to set up leave entitlements.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {leaveBalances.map((balance) => {
                    const utilizationPercentage = balance.totalDays > 0 ?
                      Math.round((balance.usedDays / balance.totalDays) * 100) : 0;

                    return (
                      <div key={balance._id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: balance.leaveTypeId.color }}
                            />
                            <div>
                              <h4 className="font-medium">{balance.leaveTypeId.name}</h4>
                              <p className="text-sm text-muted-foreground">
                                {balance.leaveTypeId.code} • {balance.leaveTypeId.isPaid ? 'Paid' : 'Unpaid'}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-semibold">
                              {balance.remainingDays} / {balance.totalDays} days
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {utilizationPercentage}% used
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Progress
                            value={utilizationPercentage}
                            className="h-3"
                            style={{
                              '--progress-background': balance.leaveTypeId.color
                            } as React.CSSProperties}
                          />

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Used:</span>
                              <span className="ml-1 font-medium">{balance.usedDays} days</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Pending:</span>
                              <span className="ml-1 font-medium">{balance.pendingDays} days</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Remaining:</span>
                              <span className="ml-1 font-medium">{balance.remainingDays} days</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Carry Over:</span>
                              <span className="ml-1 font-medium">{balance.carryOverDays} days</span>
                            </div>
                          </div>

                          {balance.notes && (
                            <div className="mt-2 p-2 bg-muted/50 rounded text-sm">
                              <span className="font-medium">Notes:</span> {balance.notes}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Leave Requests Tab */}
        <TabsContent value="requests" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Leave Requests for {year}</CardTitle>
              <CardDescription>
                History of leave requests and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {leaveRequests.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No leave requests found for {year}.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Request ID</TableHead>
                        <TableHead>Leave Type</TableHead>
                        <TableHead>Period</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Approved By</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leaveRequests.map((request) => (
                        <TableRow key={request._id}>
                          <TableCell className="font-medium">
                            {request.leaveId}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: request.leaveTypeId.color }}
                              />
                              {request.leaveTypeId.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div>{new Date(request.startDate).toLocaleDateString()}</div>
                              <div className="text-sm text-muted-foreground">
                                to {new Date(request.endDate).toLocaleDateString()}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{request.duration} days</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                request.status === 'approved' ? 'default' :
                                request.status === 'pending' ? 'secondary' :
                                request.status === 'rejected' ? 'destructive' : 'outline'
                              }
                            >
                              {request.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {request.approvedBy ? (
                              <div>
                                <div>{request.approvedBy.firstName} {request.approvedBy.lastName}</div>
                                {request.approvalDate && (
                                  <div className="text-sm text-muted-foreground">
                                    {new Date(request.approvalDate).toLocaleDateString()}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Balance History Tab */}
        <TabsContent value="history" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Balance History</CardTitle>
              <CardDescription>
                Track changes to leave balances over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert>
                <History className="h-4 w-4" />
                <AlertDescription>
                  Balance history tracking is coming soon. This will show all adjustments,
                  carry-overs, and balance changes over time.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
