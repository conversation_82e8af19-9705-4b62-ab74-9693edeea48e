"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Download, Filter, PlusCircle, Users, Eye, AlertCircle, ChevronLeft, ChevronRight } from "lucide-react"
import { LeaveBalances } from "@/components/leave-management/leave-balances"
import { useAllEmployeesLeaveBalances } from "@/hooks/use-all-employees-leave-balances"
import { useDepartmentLeaveSummaries } from "@/hooks/use-department-leave-summaries"
import { cn } from "@/lib/utils"

interface LeaveBalancesPageProps {
  userId: string
}

export function LeaveBalancesPage({ userId }: LeaveBalancesPageProps) {
  const router = useRouter()
  const [department, setDepartment] = useState<string>("all")
  const [year, setYear] = useState<string>(new Date().getFullYear().toString())
  const [currentPage, setCurrentPage] = useState(1)
  const [activeTab, setActiveTab] = useState("individual")

  // Get unique departments from employees data
  const [departments, setDepartments] = useState<string[]>([])

  // Hooks for data fetching
  const {
    employees,
    loading: employeesLoading,
    error: employeesError,
    pagination,
    fetchEmployees
  } = useAllEmployeesLeaveBalances({
    year: parseInt(year),
    department: department === "all" ? undefined : department,
    page: currentPage,
    limit: 20,
    autoFetch: true
  })

  const {
    departments: departmentSummaries,
    grandTotals,
    leaveTypes,
    loading: departmentsLoading,
    error: departmentsError,
    fetchSummaries
  } = useDepartmentLeaveSummaries({
    year: parseInt(year),
    autoFetch: true
  })

  // Extract unique departments from employees data
  useEffect(() => {
    if (employees.length > 0) {
      const uniqueDepts = Array.from(new Set(employees.map(emp => emp.employee.department)))
        .filter(dept => dept && dept.trim() !== '')
        .sort()
      setDepartments(uniqueDepts)
    }
  }, [employees])

  // Handle filter changes
  const handleDepartmentChange = (newDepartment: string) => {
    setDepartment(newDepartment)
    setCurrentPage(1)
    fetchEmployees({
      year: parseInt(year),
      department: newDepartment === "all" ? undefined : newDepartment,
      page: 1,
      limit: 20
    })
  }

  const handleYearChange = (newYear: string) => {
    setYear(newYear)
    setCurrentPage(1)
    fetchEmployees({
      year: parseInt(newYear),
      department: department === "all" ? undefined : department,
      page: 1,
      limit: 20
    })
    fetchSummaries(parseInt(newYear))
  }

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
    fetchEmployees({
      year: parseInt(year),
      department: department === "all" ? undefined : department,
      page: newPage,
      limit: 20
    })
  }

  const handleViewEmployee = (employeeId: string) => {
    router.push(`/dashboard/leave/balances/${employeeId}`)
  }

  // Generate year options (current year and 2 years back)
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 3 }, (_, i) => currentYear - i)

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Select value={department} onValueChange={handleDepartmentChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map(dept => (
                <SelectItem key={dept} value={dept}>{dept}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={year} onValueChange={handleYearChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              {yearOptions.map(yearOption => (
                <SelectItem key={yearOption} value={yearOption.toString()}>
                  {yearOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-1">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button size="sm" className="gap-1">
            <PlusCircle className="h-4 w-4" />
            <span>Adjust Balance</span>
          </Button>
        </div>
      </div>

      <LeaveBalances year={parseInt(year)} />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 md:w-[400px]">
          <TabsTrigger value="individual">Individual Balances</TabsTrigger>
          <TabsTrigger value="department">Department Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Employee Leave Balances</span>
                {pagination && (
                  <span className="text-sm font-normal text-muted-foreground">
                    {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} employees
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Detailed view of leave balances for all employees
              </CardDescription>
            </CardHeader>
            <CardContent>
              {employeesLoading ? (
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              ) : employeesError ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{employeesError}</AlertDescription>
                </Alert>
              ) : employees.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No employees found for the selected filters.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Employee</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Position</TableHead>
                          <TableHead>Total Days</TableHead>
                          <TableHead>Used Days</TableHead>
                          <TableHead>Remaining</TableHead>
                          <TableHead>Utilization</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {employees.map((employeeData) => (
                          <TableRow key={employeeData.employee._id}>
                            <TableCell className="font-medium">
                              <div>
                                <div>{employeeData.employee.firstName} {employeeData.employee.lastName}</div>
                                <div className="text-sm text-muted-foreground">
                                  {employeeData.employee.employeeId}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{employeeData.employee.department}</TableCell>
                            <TableCell>{employeeData.employee.position}</TableCell>
                            <TableCell>{employeeData.summary.totalDays}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <span>{employeeData.summary.usedDays}</span>
                                {employeeData.summary.pendingDays > 0 && (
                                  <Badge variant="secondary" className="text-xs">
                                    {employeeData.summary.pendingDays} pending
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{employeeData.summary.remainingDays}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="w-16">
                                  <Progress
                                    value={employeeData.summary.utilizationPercentage}
                                    className="h-2"
                                  />
                                </div>
                                <span className="text-sm text-muted-foreground">
                                  {employeeData.summary.utilizationPercentage}%
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewEmployee(employeeData.employee._id)}
                                className="gap-1"
                              >
                                <Eye className="h-4 w-4" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {pagination && pagination.totalPages > 1 && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Page {pagination.page} of {pagination.totalPages}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={!pagination.hasPrev}
                          className="gap-1"
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={!pagination.hasNext}
                          className="gap-1"
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="department" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Department Leave Summary</span>
                {grandTotals && (
                  <span className="text-sm font-normal text-muted-foreground">
                    {grandTotals.totalEmployees} employees across {departmentSummaries.length} departments
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Summary of leave balances by department
              </CardDescription>
            </CardHeader>
            <CardContent>
              {departmentsLoading ? (
                <div className="space-y-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <div className="space-y-2">
                        {Array.from({ length: 3 }).map((_, j) => (
                          <div key={j} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <Skeleton className="h-4 w-24" />
                              <Skeleton className="h-4 w-16" />
                            </div>
                            <Skeleton className="h-2 w-full" />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : departmentsError ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{departmentsError}</AlertDescription>
                </Alert>
              ) : departmentSummaries.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No department data found for the selected year.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  {/* Grand Totals */}
                  {grandTotals && (
                    <Card className="mb-6">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg">Organization Overview</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">{grandTotals.totalEmployees}</div>
                            <div className="text-sm text-muted-foreground">Total Employees</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">{grandTotals.totalDays}</div>
                            <div className="text-sm text-muted-foreground">Total Leave Days</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-orange-600">{grandTotals.usedDays}</div>
                            <div className="text-sm text-muted-foreground">Used Days</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">{grandTotals.utilizationPercentage}%</div>
                            <div className="text-sm text-muted-foreground">Utilization</div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Overall Utilization</span>
                            <span className="text-sm text-muted-foreground">{grandTotals.utilizationPercentage}%</span>
                          </div>
                          <Progress value={grandTotals.utilizationPercentage} className="h-3" />
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Department Summaries */}
                  <div className="space-y-6">
                    {departmentSummaries.map((dept) => (
                      <div key={dept.department} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-medium text-lg">{dept.department}</h4>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{dept.employeeCount} employees</span>
                            <span>{dept.overallSummary.utilizationPercentage}% utilized</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                          <div className="text-center p-3 bg-muted/50 rounded">
                            <div className="text-lg font-semibold">{dept.overallSummary.totalDays}</div>
                            <div className="text-xs text-muted-foreground">Total Days</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded">
                            <div className="text-lg font-semibold text-orange-600">{dept.overallSummary.usedDays}</div>
                            <div className="text-xs text-muted-foreground">Used Days</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded">
                            <div className="text-lg font-semibold text-blue-600">{dept.overallSummary.remainingDays}</div>
                            <div className="text-xs text-muted-foreground">Remaining</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded">
                            <div className="text-lg font-semibold text-amber-600">{dept.overallSummary.pendingDays}</div>
                            <div className="text-xs text-muted-foreground">Pending</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          {dept.leaveTypeSummaries.map((leaveTypeSummary) => (
                            <div key={leaveTypeSummary.leaveType._id}>
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: leaveTypeSummary.leaveType.color }}
                                  />
                                  <span className="text-sm font-medium">{leaveTypeSummary.leaveType.name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {leaveTypeSummary.employeesWithBalance} employees
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <span>{leaveTypeSummary.usedDays}/{leaveTypeSummary.totalDays} days</span>
                                  <span>({leaveTypeSummary.utilizationPercentage}%)</span>
                                </div>
                              </div>
                              <Progress
                                value={leaveTypeSummary.utilizationPercentage}
                                className="h-2"
                                style={{
                                  '--progress-background': leaveTypeSummary.leaveType.color
                                } as React.CSSProperties}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
