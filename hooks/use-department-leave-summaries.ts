"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export interface LeaveTypeSummary {
  leaveType: {
    _id: string;
    name: string;
    code: string;
    color: string;
  };
  totalDays: number;
  usedDays: number;
  pendingDays: number;
  remainingDays: number;
  utilizationPercentage: number;
  employeesWithBalance: number;
}

export interface DepartmentLeaveBalance {
  department: string;
  employeeCount: number;
  leaveTypeSummaries: LeaveTypeSummary[];
  overallSummary: {
    totalDays: number;
    usedDays: number;
    pendingDays: number;
    remainingDays: number;
    utilizationPercentage: number;
  };
}

export interface GrandTotals {
  totalEmployees: number;
  totalDays: number;
  usedDays: number;
  pendingDays: number;
  remainingDays: number;
  utilizationPercentage: number;
}

export interface LeaveTypeInfo {
  _id: string;
  name: string;
  code: string;
  color: string;
}

interface UseDepartmentLeaveSummariesOptions {
  year?: number;
  autoFetch?: boolean;
}

interface UseDepartmentLeaveSummariesReturn {
  departments: DepartmentLeaveBalance[];
  grandTotals: GrandTotals | null;
  leaveTypes: LeaveTypeInfo[];
  loading: boolean;
  error: string | null;
  fetchSummaries: (year?: number) => Promise<void>;
  refreshSummaries: () => Promise<void>;
}

export function useDepartmentLeaveSummaries(
  options: UseDepartmentLeaveSummariesOptions = {}
): UseDepartmentLeaveSummariesReturn {
  const { year = new Date().getFullYear(), autoFetch = true } = options;
  const { toast } = useToast();

  const [departments, setDepartments] = useState<DepartmentLeaveBalance[]>([]);
  const [grandTotals, setGrandTotals] = useState<GrandTotals | null>(null);
  const [leaveTypes, setLeaveTypes] = useState<LeaveTypeInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentYear, setCurrentYear] = useState(year);

  const fetchSummaries = useCallback(async (fetchYear: number = currentYear) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentYear(fetchYear);

      const response = await fetch(`/api/leave/balances/departments?year=${fetchYear}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch department leave summaries');
      }

      const data = await response.json();
      setDepartments(data.data || []);
      setGrandTotals(data.grandTotals || null);
      setLeaveTypes(data.leaveTypes || []);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching department leave summaries';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentYear, toast]);

  const refreshSummaries = useCallback(async () => {
    await fetchSummaries(currentYear);
  }, [fetchSummaries, currentYear]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchSummaries(year);
    }
  }, [autoFetch]); // Only depend on autoFetch to avoid infinite loops

  return {
    departments,
    grandTotals,
    leaveTypes,
    loading,
    error,
    fetchSummaries,
    refreshSummaries,
  };
}
