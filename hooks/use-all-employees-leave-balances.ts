"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export interface EmployeeLeaveBalance {
  employee: {
    _id: string;
    firstName: string;
    lastName: string;
    employeeId: string;
    department: string;
    position: string;
  };
  balances: Array<{
    _id: string;
    leaveTypeId: {
      _id: string;
      name: string;
      code: string;
      color: string;
      isPaid: boolean;
    };
    totalDays: number;
    usedDays: number;
    pendingDays: number;
    remainingDays: number;
    year: number;
  }>;
  summary: {
    totalDays: number;
    usedDays: number;
    pendingDays: number;
    remainingDays: number;
    utilizationPercentage: number;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface UseAllEmployeesLeaveBalancesOptions {
  year?: number;
  department?: string;
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

interface UseAllEmployeesLeaveBalancesReturn {
  employees: EmployeeLeaveBalance[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
  fetchEmployees: (options?: {
    year?: number;
    department?: string;
    page?: number;
    limit?: number;
  }) => Promise<void>;
  refreshEmployees: () => Promise<void>;
}

export function useAllEmployeesLeaveBalances(
  options: UseAllEmployeesLeaveBalancesOptions = {}
): UseAllEmployeesLeaveBalancesReturn {
  const {
    year = new Date().getFullYear(),
    department,
    page = 1,
    limit = 50,
    autoFetch = true
  } = options;

  const { toast } = useToast();

  const [employees, setEmployees] = useState<EmployeeLeaveBalance[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [currentOptions, setCurrentOptions] = useState({
    year,
    department,
    page,
    limit
  });

  const fetchEmployees = useCallback(async (fetchOptions?: {
    year?: number;
    department?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      const opts = { ...currentOptions, ...fetchOptions };
      setCurrentOptions(opts);

      // Build query parameters
      const params = new URLSearchParams({
        all: 'true',
        year: opts.year.toString(),
        page: opts.page.toString(),
        limit: opts.limit.toString()
      });

      if (opts.department && opts.department !== 'all') {
        params.append('department', opts.department);
      }

      const response = await fetch(`/api/leave/balances?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch employee leave balances');
      }

      const data = await response.json();
      setEmployees(data.data || []);
      setPagination(data.pagination || null);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching employee leave balances';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentOptions, toast]);

  const refreshEmployees = useCallback(async () => {
    await fetchEmployees(currentOptions);
  }, [fetchEmployees, currentOptions]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchEmployees({
        year,
        department,
        page,
        limit
      });
    }
  }, [autoFetch]); // Only depend on autoFetch to avoid infinite loops

  return {
    employees,
    loading,
    error,
    pagination,
    fetchEmployees,
    refreshEmployees,
  };
}
