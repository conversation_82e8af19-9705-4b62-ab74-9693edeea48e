import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { EmployeeLeaveBalanceDetailPage } from '@/components/leave-management/balances/employee-leave-balance-detail-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Employee Leave Balance Details | TCM Enterprise Business Suite',
  description: 'View detailed leave balance information for an employee',
};

interface PageProps {
  params: {
    employeeId: string;
  };
}

export default async function EmployeeLeaveBalanceDetailPageWrapper({ params }: PageProps) {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Employee Leave Balance Details"
        text="View and manage detailed leave balance information"
      />
      <div className="flex-1 space-y-6">
        <EmployeeLeaveBalanceDetailPage 
          employeeId={params.employeeId}
          currentUserId={session.user.id}
        />
      </div>
    </DashboardShell>
  );
}
