import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import Leave from '@/models/leave/Leave';
import LeaveBalance from '@/models/leave/LeaveBalance';
import { leaveAttendanceService } from '@/services/attendance/LeaveAttendanceService';
import mongoose from 'mongoose';

/**
 * POST /api/leave/requests/[id]/rollback
 * Rollback an approved or rejected leave request back to pending status
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only high-level HR roles can rollback
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Only HR Directors and above can rollback leave requests' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave request ID format' },
        { status: 400 }
      );
    }

    // Get request body for rollback reason
    const body = await req.json();
    const rollbackReason = body.reason;

    if (!rollbackReason || !rollbackReason.trim()) {
      return NextResponse.json(
        { error: 'Rollback reason is required' },
        { status: 400 }
      );
    }

    // Get leave request
    const leaveRequest = await Leave.findById(id)
      .populate('employeeId', 'firstName lastName email')
      .populate('leaveTypeId', 'name code isPaid');

    if (!leaveRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }

    // Check if leave request can be rolled back
    if (leaveRequest.status === 'pending') {
      return NextResponse.json(
        { error: 'Leave request is already pending' },
        { status: 400 }
      );
    }

    if (leaveRequest.status === 'cancelled') {
      return NextResponse.json(
        { error: 'Cannot rollback a cancelled leave request' },
        { status: 400 }
      );
    }

    // Check if approved leave has already started
    if (leaveRequest.status === 'approved') {
      const today = new Date();
      const startDate = new Date(leaveRequest.startDate);
      
      if (startDate <= today) {
        return NextResponse.json(
          { error: 'Cannot rollback approved leave that has already started or is in progress' },
          { status: 400 }
        );
      }
    }

    // Store original status for logging
    const originalStatus = leaveRequest.status;
    const originalApprovedBy = leaveRequest.approvedBy;
    const originalApprovalDate = leaveRequest.approvalDate;
    const originalRejectionReason = leaveRequest.rejectionReason;

    // Update leave request back to pending
    leaveRequest.status = 'pending';
    leaveRequest.approvedBy = undefined;
    leaveRequest.approvalDate = undefined;
    leaveRequest.rejectionReason = undefined;
    
    // Add rollback information to notes
    const rollbackNote = `\n\n[ROLLBACK ${new Date().toISOString()}] Status changed from '${originalStatus}' back to 'pending' by ${user.firstName} ${user.lastName}. Reason: ${rollbackReason}`;
    leaveRequest.notes = (leaveRequest.notes || '') + rollbackNote;

    await leaveRequest.save();

    // Update leave balance if it was previously approved
    if (originalStatus === 'approved') {
      const currentYear = new Date().getFullYear();
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: leaveRequest.employeeId,
        leaveTypeId: leaveRequest.leaveTypeId,
        year: currentYear,
      });

      if (leaveBalance) {
        // Move days from used back to pending
        leaveBalance.usedDays -= leaveRequest.duration;
        leaveBalance.pendingDays += leaveRequest.duration;
        
        // Recalculate remaining days
        leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(user.id);
        await leaveBalance.save();

        logger.info('Leave balance updated for rollback', LogCategory.HR, {
          leaveRequestId: id,
          employeeId: leaveRequest.employeeId,
          leaveTypeId: leaveRequest.leaveTypeId,
          duration: leaveRequest.duration,
          newUsedDays: leaveBalance.usedDays,
          newPendingDays: leaveBalance.pendingDays,
          newRemainingDays: leaveBalance.remainingDays
        });
      }
    } else if (originalStatus === 'rejected') {
      // If it was rejected, add days back to pending
      const currentYear = new Date().getFullYear();
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: leaveRequest.employeeId,
        leaveTypeId: leaveRequest.leaveTypeId,
        year: currentYear,
      });

      if (leaveBalance) {
        // Add days back to pending
        leaveBalance.pendingDays += leaveRequest.duration;
        
        // Recalculate remaining days
        leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(user.id);
        await leaveBalance.save();
      }
    }

    // Remove leave from attendance records if it was approved
    if (originalStatus === 'approved') {
      try {
        await leaveAttendanceService.removeLeaveFromAttendance(id, user.id);
        logger.info('Leave removed from attendance records during rollback', LogCategory.HR, {
          leaveRequestId: id,
          rolledBackBy: user.id
        });
      } catch (error) {
        logger.error('Error removing leave from attendance records during rollback', LogCategory.HR, error);
        // Don't fail the rollback if attendance update fails
      }
    }

    // Log the rollback action
    logger.info('Leave request rolled back', LogCategory.HR, {
      leaveRequestId: id,
      originalStatus,
      newStatus: 'pending',
      rolledBackBy: user.id,
      rollbackReason,
      employeeId: leaveRequest.employeeId,
      leaveType: leaveRequest.leaveTypeId,
      duration: leaveRequest.duration,
      startDate: leaveRequest.startDate,
      endDate: leaveRequest.endDate
    });

    // Populate the response data
    const populatedRequest = await Leave.findById(id)
      .populate('employeeId', 'firstName lastName email position avatar')
      .populate('leaveTypeId', 'name code color isPaid')
      .populate('createdBy', 'firstName lastName')
      .lean();

    return NextResponse.json({
      success: true,
      message: `Leave request successfully rolled back from '${originalStatus}' to 'pending'`,
      data: populatedRequest,
      rollback: {
        originalStatus,
        originalApprovedBy,
        originalApprovalDate,
        originalRejectionReason,
        rolledBackBy: {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email
        },
        rolledBackAt: new Date(),
        reason: rollbackReason
      }
    });

  } catch (error: unknown) {
    logger.error('Error rolling back leave request', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
